import { useQuery, useMutation, useQueryClient } from 'react-query';
import {
  stockDataApi,
  stockManagementApi
} from '@/services/stockApi';
import { 
  QUERY_KEYS, 
  QUERY_OPTIONS,
  clearStockCache 
} from '@/utils/queryClient';
import {
  UseStockDataResult,
  UseBatchStockDataResult,
  UseStockListResult,
  UseApiStatusResult,
  StockFlowSummary,
  KlineDataPoint
} from '@/types/stock';

/**
 * 获取单个股票数据的Hook
 * @param code 股票代码
 * @param limit 数据条数
 * @param options 查询选项
 */
export function useStockData(
  code: string,
  limit: number = 240,
  options: {
    enabled?: boolean;
    useCache?: boolean;
    refetchInterval?: number;
  } = {}
): UseStockDataResult {
  const { enabled = true, useCache = true, refetchInterval } = options;

  const query = useQuery(
    QUERY_KEYS.STOCK_DATA(code),
    () => stockDataApi.getStockData(code, limit, useCache),
    {
      ...QUERY_OPTIONS.STOCK_DATA,
      enabled: enabled && !!code,
      refetchInterval: refetchInterval ?? QUERY_OPTIONS.STOCK_DATA.refetchInterval,
    }
  );

  const data = query.data;
  const summary: StockFlowSummary | null = data?.summary || null;
  const klines: KlineDataPoint[] = data?.klines || [];
  const lastUpdate: string | null = summary?.lastUpdate || null;

  return {
    ...query,
    data: query.data,
    summary,
    klines,
    lastUpdate,
    isLoading: query.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
  };
}

/**
 * 批量获取股票数据的Hook
 * @param codes 股票代码数组
 * @param limit 数据条数
 * @param options 查询选项
 */
export function useBatchStockData(
  codes: string[],
  limit: number = 240,
  options: {
    enabled?: boolean;
    useCache?: boolean;
    refetchInterval?: number;
  } = {}
): UseBatchStockDataResult {
  const { enabled = true, useCache = true, refetchInterval } = options;

  const query = useQuery(
    QUERY_KEYS.STOCK_DATA_BATCH(codes),
    () => stockDataApi.getBatchStockData(codes, limit, useCache),
    {
      ...QUERY_OPTIONS.STOCK_DATA_BATCH,
      enabled: enabled && codes.length > 0,
      refetchInterval: refetchInterval ?? QUERY_OPTIONS.STOCK_DATA_BATCH.refetchInterval,
    }
  );

  const data = query.data;
  const results = data?.results || {};
  const errors = data?.errors || {};
  const summary = data?.summary || { total: 0, success: 0, failed: 0, fromCache: 0 };

  return {
    ...query,
    data: query.data,
    results,
    errors,
    successCount: summary.success,
    errorCount: summary.failed,
    fromCacheCount: summary.fromCache,
    isLoading: query.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
  };
}

/**
 * 股票列表管理Hook
 */
export function useStockList(): UseStockListResult {
  const queryClient = useQueryClient();

  const query = useQuery(
    QUERY_KEYS.STOCK_LIST,
    stockManagementApi.getStocks,
    QUERY_OPTIONS.STOCK_LIST
  );

  // 添加股票变更
  const addStockMutation = useMutation(
    ({ code, name }: { code: string; name?: string }) => 
      stockManagementApi.addStock(code, name),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
      },
    }
  );

  // 删除股票变更
  const removeStockMutation = useMutation(
    (code: string) => stockManagementApi.removeStock(code),
    {
      onSuccess: (_, code) => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
        clearStockCache(code);
      },
    }
  );

  // 批量添加股票变更
  const addStocksBatchMutation = useMutation(
    (stocks: Array<{ code: string; name?: string }>) => 
      stockManagementApi.addStocksBatch(stocks),
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
      },
    }
  );

  // 清空所有股票变更
  const clearAllStocksMutation = useMutation(
    stockManagementApi.clearAllStocks,
    {
      onSuccess: () => {
        queryClient.invalidateQueries(QUERY_KEYS.STOCK_LIST);
        queryClient.clear(); // 清除所有缓存
      },
    }
  );

  const stocks = query.data || [];

  return {
    ...query,
    data: query.data,
    stocks,
    isLoading: query.isLoading || addStockMutation.isLoading || removeStockMutation.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
    addStock: async (code: string, name?: string) => {
      await addStockMutation.mutateAsync({ code, name });
    },
    removeStock: async (code: string) => {
      await removeStockMutation.mutateAsync(code);
    },
    addStocksBatch: async (stocks: Array<{ code: string; name?: string }>) => {
      await addStocksBatchMutation.mutateAsync(stocks);
    },
    clearAllStocks: async () => {
      await clearAllStocksMutation.mutateAsync();
    },
  };
}

/**
 * API服务状态Hook
 */
export function useApiStatus(): UseApiStatusResult {
  const query = useQuery(
    QUERY_KEYS.API_STATUS,
    stockDataApi.getServiceStatus,
    QUERY_OPTIONS.API_STATUS
  );

  const data = query.data;
  const isHealthy = data?.isHealthy || false;
  const canMakeRequest = data?.rateLimitStatus?.canMakeRequest || false;
  const nextAvailableTime = data?.rateLimitStatus?.nextAvailableTime || 0;

  return {
    ...query,
    data: query.data,
    isHealthy,
    canMakeRequest,
    nextAvailableTime,
    isLoading: query.isLoading,
    isError: query.isError,
    isSuccess: query.isSuccess,
    isFetching: query.isFetching,
    isRefetching: query.isRefetching,
    error: query.error as Error | null,
    refetch: query.refetch,
    remove: query.remove,
  };
}

/**
 * 清除特定股票缓存的Hook
 */
export function useClearStockCache() {
  return useMutation(
    (code: string) => stockDataApi.clearCache(code),
    {
      onSuccess: (_, code) => {
        clearStockCache(code);
      },
    }
  );
}

/**
 * 批量获取股票行情数据的Hook
 * @param codes 股票代码数组
 * @param options 查询选项
 */
export function useBatchStockQuotes(
  codes: string[],
  options: {
    enabled?: boolean;
    refetchInterval?: number;
  } = {}
) {
  const { enabled = true, refetchInterval } = options;

  return useQuery(
    ['stock-quotes', codes.join(',')],
    async () => {
      if (codes.length === 0) return {};

      // 批量获取股票行情
      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};

      // 并发获取所有股票的行情数据
      const promises = codes.map(async (code) => {
        try {
          const quote = await stockDataApi.getStockQuote(code);
          results[code] = quote;
        } catch (error) {
          errors[code] = error instanceof Error ? error.message : '获取失败';
        }
      });

      await Promise.all(promises);

      return { results, errors };
    },
    {
      enabled: enabled && codes.length > 0,
      refetchInterval: refetchInterval,
      staleTime: 30000, // 30秒内认为数据是新鲜的
      cacheTime: 60000, // 缓存1分钟
    }
  );
}
