
import { Context } from 'hono';
import { EastmoneyApiService } from '../services/eastmoneyApi';
import { CacheService } from '../services/cache';
import { ApiResponse, Env, ProcessedFlowData } from '../types/api';
import { validateStockCode, sanitizeStockCode } from '../services/validator';
import { createLogger } from '../utils/logger';

/**
 * 数据获取API处理器
 */
export class DataHandler {
  private apiService: EastmoneyApiService;
  private cache: CacheService | null;
  private logger: ReturnType<typeof createLogger>;
  private cacheAvailable: boolean;

  constructor(env: Env) {
    try {
      this.logger = createLogger('DataHandler', env);
      this.apiService = new EastmoneyApiService();
      this.cacheAvailable = false;
      this.cache = null;

      // 安全初始化缓存服务
      try {
        if (env && env.STOCK_CACHE) {
          this.cache = new CacheService(env.STOCK_CACHE);
          this.cacheAvailable = true;
          this.logger.info('Cache service initialized successfully');
        } else {
          this.logger.warn('STOCK_CACHE KV namespace not available', {
            hasEnv: !!env,
            hasStockCache: !!(env && env.STOCK_CACHE),
            envKeys: env ? Object.keys(env) : []
          });
        }
      } catch (error) {
        this.logger.error('Failed to initialize cache service', {
          error: error instanceof Error ? error.message : 'Unknown error',
          hasStockCache: !!(env && env.STOCK_CACHE),
          envKeys: env ? Object.keys(env) : []
        });
        // 继续运行，但缓存不可用
        this.cache = null;
        this.cacheAvailable = false;
      }
    } catch (error) {
      console.error('DataHandler constructor failed:', error);
      // 设置默认值以防止进一步错误
      this.logger = createLogger('DataHandler');
      this.apiService = new EastmoneyApiService();
      this.cacheAvailable = false;
      this.cache = null;
    }
  }

  /**
   * 获取单个股票数据
   * GET /api/data/:code
   */
  async getStockData(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      const limit = parseInt(c.req.query('limit') || '240');
      const useCache = c.req.query('cache') !== 'false';

      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      if (limit < 1 || limit > 1000) {
        return c.json({
          success: false,
          message: '数据条数限制在1-1000之间',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cacheKey = CacheService.getStockDataKey(cleanCode);

      // 尝试从缓存获取数据
      if (useCache && this.cache && this.cacheAvailable) {
        try {
          const cachedData = await this.cache.get<ApiResponse<ProcessedFlowData>>(cacheKey);
          if (cachedData) {
            return c.json({
              ...cachedData,
              fromCache: true,
            });
          }
        } catch (error) {
          this.logger.warn('Cache get failed, continuing without cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      // 从API获取数据
      const result = await this.apiService.getStockFlowData(cleanCode, limit);

      // 缓存成功的结果
      if (result.success && this.cache && this.cacheAvailable) {
        try {
          await this.cache.set(cacheKey, result, 60); // 1分钟缓存
          await this.cache.set(CacheService.getLastUpdateKey(cleanCode), new Date().toISOString(), 300); // 5分钟缓存
        } catch (error) {
          this.logger.warn('Cache set failed, continuing without cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return c.json(result, result.success ? 200 : 500);
    } catch (error) {
      console.error('获取股票数据失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取股票实时行情
   * GET /api/quote/:code
   */
  async getStockQuote(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');

      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 从API获取实时行情数据
      const result = await this.apiService.getStockQuote(cleanCode);

      return c.json(result, result.success ? 200 : 500);
    } catch (error) {
      console.error('获取股票实时行情失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 批量获取股票数据
   * GET /api/data/batch
   */
  async getBatchStockData(c: Context): Promise<Response> {
    try {
      console.log('=== 批量股票数据请求开始 ===');

      const codesParam = c.req.query('codes');
      const limit = parseInt(c.req.query('limit') || '240');

      console.log('请求参数:', { codesParam, limit });

      if (!codesParam) {
        return c.json({
          success: false,
          message: '股票代码列表不能为空',
          timestamp: new Date().toISOString(),
        }, 400);
      }

      const codes = codesParam.split(',').map(code => code.trim()).filter(Boolean);
      console.log('处理后的股票代码:', codes);

      if (codes.length === 0) {
        return c.json({
          success: false,
          message: '有效的股票代码不能为空',
          timestamp: new Date().toISOString(),
        }, 400);
      }

      if (codes.length > 20) {
        return c.json({
          success: false,
          message: '一次最多只能查询20个股票',
          timestamp: new Date().toISOString(),
        }, 400);
      }

      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};

      console.log('开始处理股票数据...');

      // 使用已验证工作的简化逻辑
      for (const code of codes) {
        console.log(`处理股票: ${code}`);

        try {
          // 构建URL - 直接使用东方财富API
          const marketCode = code.startsWith('6') ? 1 : 0;
          const secid = `${marketCode}.${code}`;

          const params = new URLSearchParams({
            secid,
            klt: '1',
            lmt: limit.toString(),
            fields1: 'f1,f2,f3,f7',
            fields2: 'f51,f52,f53,f54,f55,f56,f57,f58,f59,f60,f61,f62,f63',
          });

          const url = `https://push2.eastmoney.com/api/qt/stock/fflow/kline/get?${params.toString()}`;

          console.log(`${code} 请求URL:`, url);

          const response = await fetch(url, {
            method: 'GET',
            headers: {
              'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
              'Accept': '*/*',
              'Referer': `https://data.eastmoney.com/zjlx/${code}.html`,
              'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
              'Cache-Control': 'no-cache',
              'Pragma': 'no-cache',
            },
          });

          console.log(`${code} 响应状态:`, response.status);

          if (!response.ok) {
            errors[code] = `HTTP ${response.status}: ${response.statusText}`;
            continue;
          }

          const responseText = await response.text();
          console.log(`${code} 响应长度:`, responseText.length);

          const jsonData = JSON.parse(responseText);
          console.log(`${code} JSON解析成功`);

          // 简单验证
          if (jsonData.rc === 0 && jsonData.data && jsonData.data.code && jsonData.data.klines) {
            // 简化的数据处理
            const processedKlines = jsonData.data.klines.map((kline: string) => {
              const parts = kline.split(',');
              const [time, mainNet, superLargeNet, largeNet, mediumNet, smallNet] = parts;
              return {
                time,
                mainNetInflow: parseFloat(mainNet),
                superLargeNetInflow: parseFloat(superLargeNet),
                largeNetInflow: parseFloat(largeNet),
                mediumNetInflow: parseFloat(mediumNet),
                smallNetInflow: parseFloat(smallNet),
              };
            });

            const summary = {
              code: jsonData.data.code,
              name: jsonData.data.name,
              market: jsonData.data.market,
              lastUpdate: new Date().toISOString().slice(0, 16).replace('T', ' '),
              mainNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.mainNetInflow, 0),
              superLargeNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.superLargeNetInflow, 0),
              largeNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.largeNetInflow, 0),
              mediumNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.mediumNetInflow, 0),
              smallNetInflow: processedKlines.reduce((sum: number, k: any) => sum + k.smallNetInflow, 0),
            };

            results[code] = {
              summary,
              klines: processedKlines,
              totalCount: processedKlines.length,
            };

            console.log(`${code} 处理成功`);
          } else {
            errors[code] = `无效数据结构: rc=${jsonData.rc}`;
            console.log(`${code} 验证失败`);
          }

          // 添加延迟
          if (codes.indexOf(code) < codes.length - 1) {
            await new Promise(resolve => setTimeout(resolve, 200));
          }

        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : '未知错误';
          errors[code] = errorMsg;
          console.error(`${code} 处理异常:`, errorMsg);
        }
      }

      // 临时修复：使用简化的串行处理逻辑
      this.logger.info('Starting simplified batch processing', { codes, limit });

      for (const code of codes) {
        this.logger.debug('Processing stock', { code });

        try {
          // 直接调用API服务，跳过缓存逻辑以简化调试
          const result = await this.apiService.getStockFlowData(code, limit);

          this.logger.debug('API call completed', {
            code,
            success: result.success,
            message: result.message
          });

          if (result.success && result.data) {
            results[code] = result.data;
            this.logger.debug('Stock data added to results', { code });
          } else {
            const errorMsg = result.message || '获取失败';
            errors[code] = errorMsg;
            this.logger.warn('Stock data fetch failed', { code, error: errorMsg });
          }

          // 添加延迟以避免频率限制
          if (codes.indexOf(code) < codes.length - 1) {
            this.logger.debug('Adding delay before next stock');
            await new Promise(resolve => setTimeout(resolve, 200));
          }

        } catch (error) {
          const errorMsg = error instanceof Error ? error.message : '未知错误';
          errors[code] = errorMsg;
          this.logger.error('Exception during stock processing', {
            code,
            error: errorMsg,
            stack: error instanceof Error ? error.stack : undefined
          });
        }
      }

      this.logger.info('Batch processing completed', {
        totalCodes: codes.length,
        successCount: Object.keys(results).length,
        errorCount: Object.keys(errors).length
      });

      const successCount = Object.keys(results).length;
      const errorCount = Object.keys(errors).length;

      console.log('批量处理完成:', {
        total: codes.length,
        success: successCount,
        failed: errorCount,
        errors: errorCount > 0 ? errors : undefined
      });

      const responseData = {
        success: successCount > 0,
        data: {
          results,
          errors,
          summary: {
            total: codes.length,
            success: successCount,
            failed: errorCount,
            fromCache: 0,
          },
        },
        message: `成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
        timestamp: new Date().toISOString(),
      };

      const statusCode = successCount > 0 ? 200 : 500;

      this.logger.debug('Sending batch response', {
        statusCode,
        hasResults: successCount > 0,
        responseSize: JSON.stringify(responseData).length
      });

      return c.json(responseData, statusCode);
    } catch (error) {
      this.logger.error('Batch stock data processing failed', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取股票最后更新时间
   * GET /api/data/:code/last-update
   */
  async getLastUpdate(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      let lastUpdate: string | null = null;

      if (this.cache && this.cacheAvailable) {
        try {
          lastUpdate = await this.cache.get<string>(CacheService.getLastUpdateKey(cleanCode));
        } catch (error) {
          this.logger.warn('Failed to get last update from cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
        }
      }

      return c.json({
        success: true,
        data: {
          code: cleanCode,
          lastUpdate: lastUpdate || null,
          hasData: lastUpdate !== null,
          cacheAvailable: this.cacheAvailable,
        },
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('获取最后更新时间失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 调试批量处理 - 临时方法
   * GET /api/debug/batch-handler
   */
  async debugBatchStockData(c: Context): Promise<Response> {
    try {
      const codesParam = c.req.query('codes') || '600121';
      const limit = parseInt(c.req.query('limit') || '20');
      const useCache = c.req.query('cache') !== 'false';

      this.logger.debug('Debug batch handler start', { codesParam, limit, useCache });

      if (!codesParam) {
        return c.json({
          success: false,
          message: '股票代码列表不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const codes = codesParam.split(',').map(code => sanitizeStockCode(code.trim())).filter(Boolean);
      this.logger.debug('Debug processed codes', { codes });

      if (codes.length === 0) {
        return c.json({
          success: false,
          message: '有效的股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const results: Record<string, any> = {};
      const errors: Record<string, string> = {};

      // 只处理第一个股票进行调试
      const code = codes[0];
      this.logger.debug('Debug processing single code', { code });

      try {
        this.logger.debug('Debug calling apiService.getStockFlowData', { code, limit });
        const result = await this.apiService.getStockFlowData(code, limit);

        this.logger.debug('Debug API result received', {
          code,
          success: result.success,
          message: result.message,
          hasData: !!result.data,
          dataKeys: result.data ? Object.keys(result.data) : []
        });

        if (result.success) {
          results[code] = result.data;
          this.logger.debug('Debug result added to results', { code });
        } else {
          const errorMsg = result.message || '获取失败';
          errors[code] = errorMsg;
          this.logger.debug('Debug result failed', { code, error: errorMsg });
        }
      } catch (error) {
        const errorMsg = error instanceof Error ? error.message : '未知错误';
        errors[code] = errorMsg;
        this.logger.error('Debug exception caught', { code, error: errorMsg });
      }

      const successCount = Object.keys(results).length;
      const errorCount = Object.keys(errors).length;

      this.logger.debug('Debug batch handler completed', {
        successCount,
        errorCount,
        results: Object.keys(results),
        errors
      });

      return c.json({
        success: successCount > 0,
        data: {
          results,
          errors,
          summary: {
            total: codes.length,
            success: successCount,
            failed: errorCount,
            fromCache: 0,
          },
        },
        message: `调试：成功获取 ${successCount} 个股票数据${errorCount > 0 ? `，${errorCount} 个失败` : ''}`,
        timestamp: new Date().toISOString(),
      } as ApiResponse, successCount > 0 ? 200 : 500);

    } catch (error) {
      this.logger.error('Debug batch handler error', {
        error: error instanceof Error ? error.message : 'Unknown error',
        stack: error instanceof Error ? error.stack : undefined
      });

      return c.json({
        success: false,
        message: '调试：服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 清除股票数据缓存
   * DELETE /api/data/:code/cache
   */
  async clearCache(c: Context): Promise<Response> {
    try {
      const code = c.req.param('code');
      
      if (!code) {
        return c.json({
          success: false,
          message: '股票代码不能为空',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      const cleanCode = sanitizeStockCode(code);
      if (!validateStockCode(cleanCode)) {
        return c.json({
          success: false,
          message: '无效的股票代码格式',
          timestamp: new Date().toISOString(),
        } as ApiResponse, 400);
      }

      // 清除相关缓存
      if (this.cache && this.cacheAvailable) {
        try {
          await Promise.all([
            this.cache.delete(CacheService.getStockDataKey(cleanCode)),
            this.cache.delete(CacheService.getLastUpdateKey(cleanCode)),
          ]);
        } catch (error) {
          this.logger.warn('Failed to clear cache', {
            code: cleanCode,
            error: error instanceof Error ? error.message : 'Unknown error'
          });
          return c.json({
            success: false,
            message: '缓存清除失败',
            timestamp: new Date().toISOString(),
          } as ApiResponse, 500);
        }
      }

      return c.json({
        success: true,
        data: {
          code: cleanCode,
          cacheAvailable: this.cacheAvailable,
          cleared: this.cacheAvailable
        },
        message: this.cacheAvailable ? '缓存清除成功' : '缓存不可用，无需清除',
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      console.error('清除缓存失败:', error);
      return c.json({
        success: false,
        message: '服务器内部错误',
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }

  /**
   * 获取API服务状态
   * GET /api/data/status
   */
  async getServiceStatus(c: Context): Promise<Response> {
    try {
      // 获取基础 API 服务状态
      const apiStatus = this.apiService.getServiceStatus();

      // 构建完整状态信息
      const statusData: any = {
        ...apiStatus,
        cacheAvailable: this.cacheAvailable,
        environment: c.env?.ENVIRONMENT || 'unknown',
        timestamp: new Date().toISOString(),
      };

      // 如果缓存不可用，添加警告信息
      if (!this.cacheAvailable) {
        this.logger.warn('Cache service not available in status check');
        statusData.warnings = ['Cache service is not available'];
      }

      return c.json({
        success: true,
        data: statusData,
        timestamp: new Date().toISOString(),
      } as ApiResponse);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error';

      this.logger.error('获取服务状态失败', {
        error: errorMessage,
        env: Object.keys(c.env || {}),
        cacheAvailable: this.cacheAvailable
      });

      return c.json({
        success: false,
        message: '服务器内部错误',
        error: errorMessage,
        timestamp: new Date().toISOString(),
      } as ApiResponse, 500);
    }
  }
}

