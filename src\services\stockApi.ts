import {
  ApiResponse,
  StockInfo,
  ProcessedStockData,
  BatchStockDataResponse,
  StockLastUpdate,
  ApiServiceStatus,
  StockApiError,
  StockQuote
} from '@/types/stock';

/**
 * API基础配置
 */
const API_BASE_URL = import.meta.env.VITE_API_BASE_URL || 'http://localhost:8787';

/**
 * 创建API错误
 */
const createApiError = (message: string, status?: number, details?: any): StockApiError => {
  const error = new Error(message) as StockApiError;
  error.status = status;
  error.details = details;
  return error;
};

/**
 * 带重试机制的API请求函数
 */
const apiRequestWithRetry = async <T>(
  endpoint: string,
  options: RequestInit = {},
  maxRetries: number = 3,
  retryDelay: number = 1000
): Promise<ApiResponse<T>> => {
  const url = `${API_BASE_URL}${endpoint}`;

  const defaultOptions: RequestInit = {
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
    },
  };

  let lastError: Error;

  for (let attempt = 1; attempt <= maxRetries; attempt++) {
    try {
      const response = await fetch(url, { ...defaultOptions, ...options });

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        const error = createApiError(
          `HTTP ${response.status}: ${response.statusText}`,
          response.status,
          errorText
        );

        // 对于400错误（客户端错误），不进行重试
        if (response.status >= 400 && response.status < 500) {
          throw error;
        }

        // 对于5xx错误，进行重试
        if (attempt === maxRetries) {
          throw error;
        }

        console.warn(`API请求失败 (尝试 ${attempt}/${maxRetries}):`, error.message);
        lastError = error;
      } else {
        const data = await response.json();

        // 验证响应格式
        if (typeof data !== 'object' || !('success' in data)) {
          throw createApiError('Invalid API response format');
        }

        return data as ApiResponse<T>;
      }
    } catch (error) {
      if (error instanceof TypeError && error.message.includes('fetch')) {
        const networkError = createApiError('Network error: Unable to connect to server');

        if (attempt === maxRetries) {
          throw networkError;
        }

        console.warn(`网络错误 (尝试 ${attempt}/${maxRetries}):`, networkError.message);
        lastError = networkError;
      } else {
        // 对于其他错误，直接抛出
        throw error;
      }
    }

    // 等待后重试
    if (attempt < maxRetries) {
      await new Promise(resolve => setTimeout(resolve, retryDelay * attempt));
    }
  }

  throw lastError!;
};

/**
 * 通用API请求函数（保持向后兼容）
 */
const apiRequest = async <T>(
  endpoint: string,
  options: RequestInit = {}
): Promise<ApiResponse<T>> => {
  return apiRequestWithRetry<T>(endpoint, options, 2, 500);
};

/**
 * 股票管理API
 */
export const stockManagementApi = {
  /**
   * 获取股票列表
   */
  async getStocks(): Promise<StockInfo[]> {
    const response = await apiRequest<StockInfo[]>('/api/stocks');
    if (!response.success) {
      throw createApiError(response.message || 'Failed to get stocks');
    }
    return response.data || [];
  },

  /**
   * 添加股票
   */
  async addStock(code: string, name?: string): Promise<StockInfo> {
    const response = await apiRequest<StockInfo>('/api/stocks', {
      method: 'POST',
      body: JSON.stringify({ code, name }),
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to add stock');
    }
    
    return response.data!;
  },

  /**
   * 删除股票
   */
  async removeStock(code: string): Promise<StockInfo> {
    const response = await apiRequest<StockInfo>(`/api/stocks/${code}`, {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to remove stock');
    }
    
    return response.data!;
  },

  /**
   * 批量添加股票
   */
  async addStocksBatch(stocks: Array<{ code: string; name?: string }>): Promise<{
    added: StockInfo[];
    errors: string[];
    total: number;
  }> {
    const response = await apiRequest('/api/stocks/batch', {
      method: 'POST',
      body: JSON.stringify({ stocks }),
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to add stocks');
    }
    
    return response.data as any;
  },

  /**
   * 清空所有股票
   */
  async clearAllStocks(): Promise<{ deletedCount: number }> {
    const response = await apiRequest('/api/stocks', {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to clear stocks');
    }
    
    return response.data as any;
  },
};

/**
 * 批量请求监控工具
 */
const BatchRequestMonitor = {
  /**
   * 记录批量请求开始
   */
  logBatchStart: (totalCodes: number, batchCount: number) => {
    console.group(`🚀 批量股票数据请求开始`);
    console.log(`📊 总股票数: ${totalCodes}`);
    console.log(`📦 分批数量: ${batchCount}`);
    console.log(`⏰ 开始时间: ${new Date().toLocaleTimeString()}`);
  },

  /**
   * 记录批量请求完成
   */
  logBatchComplete: (summary: { total: number; success: number; failed: number; fromCache: number }, duration: number) => {
    console.log(`✅ 批量请求完成`);
    console.log(`📈 成功率: ${((summary.success / summary.total) * 100).toFixed(1)}%`);
    console.log(`⚡ 耗时: ${duration}ms`);
    console.log(`📋 详情: 成功${summary.success}个，失败${summary.failed}个，缓存${summary.fromCache}个`);
    console.groupEnd();
  },

  /**
   * 记录批次处理状态
   */
  logBatchProgress: (current: number, total: number, success: number, failed: number) => {
    const progress = ((current / total) * 100).toFixed(1);
    console.log(`📊 进度: ${progress}% (${current}/${total}) | 成功: ${success} | 失败: ${failed}`);
  }
};

/**
 * 股票数据API
 */
export const stockDataApi = {
  /**
   * 获取单个股票数据
   */
  async getStockData(code: string, limit: number = 240, useCache: boolean = true): Promise<ProcessedStockData> {
    const params = new URLSearchParams({
      limit: limit.toString(),
      cache: useCache.toString(),
    });
    
    const response = await apiRequest<ProcessedStockData>(`/api/data/${code}?${params}`);
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to get stock data');
    }
    
    return response.data!;
  },

  /**
   * 批量获取股票数据 - 支持自动分批处理
   */
  async getBatchStockData(codes: string[], limit: number = 240, useCache: boolean = true): Promise<BatchStockDataResponse> {
    // API限制：每次最多20个股票
    const BATCH_SIZE = 20;

    if (codes.length === 0) {
      return {
        results: {},
        errors: {},
        summary: {
          total: 0,
          success: 0,
          failed: 0,
          fromCache: 0,
        },
      };
    }

    // 如果股票数量不超过限制，直接调用
    if (codes.length <= BATCH_SIZE) {
      return this.getBatchStockDataSingle(codes, limit, useCache);
    }

    // 分批处理
    const allResults: Record<string, any> = {};
    const allErrors: Record<string, string> = {};
    let totalSuccess = 0;
    let totalFailed = 0;
    let totalFromCache = 0;

    // 将股票代码分成多个批次
    const batches: string[][] = [];
    for (let i = 0; i < codes.length; i += BATCH_SIZE) {
      batches.push(codes.slice(i, i + BATCH_SIZE));
    }

    // 开始监控
    const startTime = Date.now();
    BatchRequestMonitor.logBatchStart(codes.length, batches.length);

    // 并发处理所有批次（限制并发数避免过载）
    const MAX_CONCURRENT_BATCHES = 3;
    const processBatch = async (batch: string[], batchIndex: number) => {
      const batchId = `${batchIndex + 1}/${batches.length}`;

      try {
        console.log(`[批次 ${batchId}] 开始处理：${batch.join(',')}`);

        // 使用重试机制处理单个批次
        const batchResult = await apiRequestWithRetry<BatchStockDataResponse>(
          `/api/stocks/batch-data?${new URLSearchParams({
            codes: batch.join(','),
            limit: limit.toString(),
            cache: useCache.toString(),
          })}`,
          {},
          3, // 最多重试3次
          1000 // 重试延迟1秒
        );

        if (!batchResult.success) {
          throw createApiError(batchResult.message || `批次 ${batchId} 处理失败`);
        }

        const data = batchResult.data!;

        // 合并结果
        Object.assign(allResults, data.results);
        Object.assign(allErrors, data.errors);
        totalSuccess += data.summary.success;
        totalFailed += data.summary.failed;
        totalFromCache += data.summary.fromCache;

        console.log(`[批次 ${batchId}] 完成：成功${data.summary.success}个，失败${data.summary.failed}个`);
      } catch (error) {
        console.error(`[批次 ${batchId}] 处理失败:`, error);

        // 将整个批次标记为失败
        const errorMessage = error instanceof Error ? error.message : '批次处理失败';
        batch.forEach(code => {
          allErrors[code] = `批次失败: ${errorMessage}`;
        });
        totalFailed += batch.length;
      }
    };

    // 分组并发处理
    for (let i = 0; i < batches.length; i += MAX_CONCURRENT_BATCHES) {
      const concurrentBatches = batches.slice(i, i + MAX_CONCURRENT_BATCHES);
      const promises = concurrentBatches.map((batch, index) =>
        processBatch(batch, i + index)
      );

      await Promise.all(promises);

      // 批次间添加小延迟，避免API频率限制
      if (i + MAX_CONCURRENT_BATCHES < batches.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    // 完成监控
    const duration = Date.now() - startTime;
    BatchRequestMonitor.logBatchComplete({
      total: codes.length,
      success: totalSuccess,
      failed: totalFailed,
      fromCache: totalFromCache,
    }, duration);

    return {
      results: allResults,
      errors: allErrors,
      summary: {
        total: codes.length,
        success: totalSuccess,
        failed: totalFailed,
        fromCache: totalFromCache,
      },
    };
  },

  /**
   * 单批次获取股票数据（内部方法）
   */
  private async getBatchStockDataSingle(codes: string[], limit: number = 240, useCache: boolean = true): Promise<BatchStockDataResponse> {
    const params = new URLSearchParams({
      codes: codes.join(','),
      limit: limit.toString(),
      cache: useCache.toString(),
    });

    // 使用修复后的批量API端点
    const response = await apiRequest<BatchStockDataResponse>(`/api/stocks/batch-data?${params}`);

    if (!response.success) {
      throw createApiError(response.message || 'Failed to get batch stock data');
    }

    return response.data!;
  },

  /**
   * 获取股票实时行情（包含涨跌幅）
   */
  async getStockQuote(code: string): Promise<StockQuote> {
    const response = await apiRequest<StockQuote>(`/api/quote/${code}`);

    if (!response.success) {
      throw createApiError(response.message || 'Failed to get stock quote');
    }

    return response.data!;
  },

  /**
   * 获取股票最后更新时间
   */
  async getLastUpdate(code: string): Promise<StockLastUpdate> {
    const response = await apiRequest<StockLastUpdate>(`/api/data/${code}/last-update`);
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to get last update');
    }
    
    return response.data!;
  },

  /**
   * 清除股票缓存
   */
  async clearCache(code: string): Promise<{ code: string }> {
    const response = await apiRequest(`/api/data/${code}/cache`, {
      method: 'DELETE',
    });
    
    if (!response.success) {
      throw createApiError(response.message || 'Failed to clear cache');
    }
    
    return response.data as any;
  },

  /**
   * 获取API服务状态
   */
  async getServiceStatus(): Promise<ApiServiceStatus> {
    // 使用新的端点避免旧端点的缓存问题
    const response = await apiRequest<ApiServiceStatus>('/api/service-status');

    if (!response.success) {
      throw createApiError(response.message || 'Failed to get service status');
    }

    return response.data!;
  },
};

/**
 * 工具API
 */
export const utilsApi = {
  /**
   * 健康检查
   */
  async healthCheck(): Promise<any> {
    const response = await apiRequest('/health');
    return response;
  },

  /**
   * API测试
   */
  async testApi(): Promise<any> {
    const response = await apiRequest('/api/test');
    return response;
  },
};

/**
 * 导出便捷函数
 */
export const getStocks = stockManagementApi.getStocks;
export const addStock = stockManagementApi.addStock;
export const removeStock = stockManagementApi.removeStock;
export const getStockData = stockDataApi.getStockData;
export const getBatchStockData = stockDataApi.getBatchStockData;
export const getServiceStatus = stockDataApi.getServiceStatus;
